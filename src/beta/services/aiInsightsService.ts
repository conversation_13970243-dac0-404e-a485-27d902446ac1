import { createClient } from "@/shared/services/supabase/client";
import { PersonalFinanceService } from "../dashboards/personal/services/personalFinanceService";
import { FamilyFinanceService } from "../dashboards/family/services/familyFinanceService";
import { CombinedFinanceService } from "../dashboards/combined/services/combinedFinanceService";

export interface AIInsight {
  id: string;
  type: 
    | "budget_optimization"
    | "spending_pattern"
    | "goal_acceleration"
    | "risk_warning"
    | "opportunity"
    | "prediction"
    | "trend_analysis"
    | "cash_flow_optimization";
  priority: "low" | "medium" | "high";
  title: string;
  description: string;
  impact: string;
  confidence: number; // 0-100
  category: string;
  actionable: boolean;
  timeframe?: string;
  action_text?: string;
  related_data?: Record<string, any>;
  created_at: string;
}

export interface AIInsightsSummary {
  total_insights: number;
  high_priority_count: number;
  actionable_count: number;
  potential_savings: number;
  confidence_score: number;
  last_updated: string;
}

export interface SpendingPattern {
  category: string;
  average_monthly: number;
  trend: "increasing" | "decreasing" | "stable";
  variance: number;
  seasonal_factor?: number;
}

export interface BudgetOptimization {
  category: string;
  current_budget: number;
  suggested_budget: number;
  potential_savings: number;
  confidence: number;
  reasoning: string;
}

export interface GoalAcceleration {
  goal_id: string;
  goal_name: string;
  current_timeline: number; // months
  optimized_timeline: number; // months
  required_monthly_increase: number;
  suggested_actions: string[];
}

export class AIInsightsService {
  private supabase = createClient();
  private personalService = new PersonalFinanceService();
  private familyService = new FamilyFinanceService();
  private combinedService = new CombinedFinanceService();

  // Main method to generate AI insights for a user
  async generateInsights(userId: string, financeType: 'personal' | 'family' | 'combined'): Promise<AIInsight[]> {
    try {
      console.log('🔗 Using REAL AI Insights Service - generateInsights for:', userId, 'type:', financeType);

      const insights: AIInsight[] = [];

      switch (financeType) {
        case 'personal':
          const personalInsights = await this.generatePersonalInsights(userId);
          insights.push(...personalInsights);
          break;
        case 'family':
          const familyInsights = await this.generateFamilyInsights(userId);
          insights.push(...familyInsights);
          break;
        case 'combined':
          const combinedInsights = await this.generateCombinedInsights(userId);
          insights.push(...combinedInsights);
          break;
      }

      // Sort by priority and confidence
      return insights.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return b.confidence - a.confidence;
      });
    } catch (error) {
      console.error("Error generating AI insights:", error);
      return [];
    }
  }

  // Generate insights summary
  async getInsightsSummary(userId: string, financeType: 'personal' | 'family' | 'combined'): Promise<AIInsightsSummary> {
    try {
      const insights = await this.generateInsights(userId, financeType);
      
      const highPriorityCount = insights.filter(i => i.priority === 'high').length;
      const actionableCount = insights.filter(i => i.actionable).length;
      
      // Calculate potential savings from insights
      const potentialSavings = insights.reduce((total, insight) => {
        if (insight.related_data?.potential_savings) {
          return total + insight.related_data.potential_savings;
        }
        return total;
      }, 0);

      // Calculate average confidence
      const avgConfidence = insights.length > 0 
        ? insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length 
        : 0;

      return {
        total_insights: insights.length,
        high_priority_count: highPriorityCount,
        actionable_count: actionableCount,
        potential_savings: potentialSavings,
        confidence_score: Math.round(avgConfidence),
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error generating insights summary:", error);
      return {
        total_insights: 0,
        high_priority_count: 0,
        actionable_count: 0,
        potential_savings: 0,
        confidence_score: 0,
        last_updated: new Date().toISOString(),
      };
    }
  }

  // Generate personal finance insights
  private async generatePersonalInsights(userId: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const data = await this.personalService.getPersonalFinanceData(userId);
      
      // Analyze spending patterns
      const spendingInsights = await this.analyzeSpendingPatterns(data.transactions, 'personal');
      insights.push(...spendingInsights);

      // Analyze budget performance
      const budgetInsights = await this.analyzeBudgetPerformance(data.budgets, 'personal');
      insights.push(...budgetInsights);

      // Analyze goal progress
      const goalInsights = await this.analyzeGoalProgress(data.goals, 'personal');
      insights.push(...goalInsights);

      // Analyze cash flow
      const cashFlowInsights = await this.analyzeCashFlow(data.analytics.overview, 'personal');
      insights.push(...cashFlowInsights);

      return insights;
    } catch (error) {
      console.error("Error generating personal insights:", error);
      return [];
    }
  }

  // Generate family finance insights
  private async generateFamilyInsights(userId: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const data = await this.familyService.getFamilyFinanceData(userId);
      
      // Analyze family spending patterns
      const spendingInsights = await this.analyzeSpendingPatterns(data.transactions, 'family');
      insights.push(...spendingInsights);

      // Analyze family budget performance
      const budgetInsights = await this.analyzeBudgetPerformance(data.budgets, 'family');
      insights.push(...budgetInsights);

      // Analyze family goal progress
      const goalInsights = await this.analyzeGoalProgress(data.goals, 'family');
      insights.push(...goalInsights);

      // Analyze family collaboration patterns
      const collaborationInsights = await this.analyzeFamilyCollaboration(data);
      insights.push(...collaborationInsights);

      return insights;
    } catch (error) {
      console.error("Error generating family insights:", error);
      return [];
    }
  }

  // Generate combined finance insights
  private async generateCombinedInsights(userId: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const data = await this.combinedService.getCombinedFinanceData(userId);
      
      // Cross-account optimization insights
      const crossAccountInsights = await this.analyzeCrossAccountOptimization(data);
      insights.push(...crossAccountInsights);

      // Unified goal alignment insights
      const goalAlignmentInsights = await this.analyzeGoalAlignment(data);
      insights.push(...goalAlignmentInsights);

      // Overall financial health insights
      const healthInsights = await this.analyzeOverallFinancialHealth(data);
      insights.push(...healthInsights);

      return insights;
    } catch (error) {
      console.error("Error generating combined insights:", error);
      return [];
    }
  }

  // Analyze spending patterns for insights
  private async analyzeSpendingPatterns(transactions: any[], context: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      // Group transactions by category
      const categorySpending = new Map<string, number>();
      const currentMonth = new Date();
      currentMonth.setDate(1);

      transactions
        .filter(t => new Date(t.date) >= currentMonth && t.type === 'expense')
        .forEach(t => {
          const category = t.category?.name || 'Uncategorized';
          categorySpending.set(category, (categorySpending.get(category) || 0) + Math.abs(t.amount));
        });

      // Find categories with unusual spending
      for (const [category, amount] of categorySpending.entries()) {
        if (amount > 500) { // Threshold for high spending
          insights.push({
            id: `high-spending-${category.toLowerCase().replace(/\s+/g, '-')}`,
            type: "spending_pattern",
            priority: amount > 1000 ? "high" : "medium",
            title: `High Spending in ${category}`,
            description: `You've spent $${amount.toFixed(2)} on ${category} this month, which is above average.`,
            impact: `$${(amount * 0.2).toFixed(2)} potential monthly savings`,
            confidence: 75,
            category: category,
            actionable: true,
            timeframe: "This month",
            action_text: "Review spending",
            related_data: {
              category,
              amount,
              potential_savings: amount * 0.2,
            },
            created_at: new Date().toISOString(),
          });
        }
      }

      return insights;
    } catch (error) {
      console.error("Error analyzing spending patterns:", error);
      return [];
    }
  }

  // Analyze budget performance
  private async analyzeBudgetPerformance(budgets: any[], context: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      budgets.forEach(budget => {
        if (budget.status === 'exceeded') {
          insights.push({
            id: `budget-exceeded-${budget.id}`,
            type: "risk_warning",
            priority: "high",
            title: `Budget Exceeded: ${budget.name}`,
            description: `You've exceeded your ${budget.name} budget by $${(budget.spent - budget.amount).toFixed(2)}.`,
            impact: `$${(budget.spent - budget.amount).toFixed(2)} overspend`,
            confidence: 95,
            category: "Budgets",
            actionable: true,
            timeframe: "Immediate",
            action_text: "Adjust spending",
            related_data: {
              budget_id: budget.id,
              overspend: budget.spent - budget.amount,
            },
            created_at: new Date().toISOString(),
          });
        } else if (budget.status === 'warning') {
          insights.push({
            id: `budget-warning-${budget.id}`,
            type: "prediction",
            priority: "medium",
            title: `Budget Warning: ${budget.name}`,
            description: `You're at ${budget.percentage_used.toFixed(1)}% of your ${budget.name} budget.`,
            impact: `$${budget.remaining.toFixed(2)} remaining`,
            confidence: 80,
            category: "Budgets",
            actionable: true,
            timeframe: "This period",
            action_text: "Monitor spending",
            related_data: {
              budget_id: budget.id,
              percentage_used: budget.percentage_used,
            },
            created_at: new Date().toISOString(),
          });
        }
      });

      return insights;
    } catch (error) {
      console.error("Error analyzing budget performance:", error);
      return [];
    }
  }

  // Analyze goal progress
  private async analyzeGoalProgress(goals: any[], context: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      goals.forEach(goal => {
        if (goal.status === 'active') {
          // Check if goal is behind schedule
          if (goal.target_date && goal.progress_percentage < 50) {
            const targetDate = new Date(goal.target_date);
            const today = new Date();
            const totalDays = targetDate.getTime() - new Date(goal.created_at).getTime();
            const elapsedDays = today.getTime() - new Date(goal.created_at).getTime();
            const expectedProgress = (elapsedDays / totalDays) * 100;

            if (goal.progress_percentage < expectedProgress - 10) {
              insights.push({
                id: `goal-behind-${goal.id}`,
                type: "goal_acceleration",
                priority: "medium",
                title: `Goal Behind Schedule: ${goal.name}`,
                description: `Your ${goal.name} goal is ${(expectedProgress - goal.progress_percentage).toFixed(1)}% behind schedule.`,
                impact: `${goal.days_remaining} days remaining`,
                confidence: 85,
                category: "Goals",
                actionable: true,
                timeframe: `${goal.days_remaining} days`,
                action_text: "Increase contributions",
                related_data: {
                  goal_id: goal.id,
                  behind_percentage: expectedProgress - goal.progress_percentage,
                },
                created_at: new Date().toISOString(),
              });
            }
          }

          // Check for milestone achievements
          if (goal.progress_percentage >= 25 && goal.progress_percentage < 30) {
            insights.push({
              id: `goal-milestone-${goal.id}`,
              type: "opportunity",
              priority: "low",
              title: `Milestone Reached: ${goal.name}`,
              description: `Congratulations! You're 25% of the way to your ${goal.name} goal.`,
              impact: "Motivation boost",
              confidence: 100,
              category: "Goals",
              actionable: false,
              related_data: {
                goal_id: goal.id,
                milestone: 25,
              },
              created_at: new Date().toISOString(),
            });
          }
        }
      });

      return insights;
    } catch (error) {
      console.error("Error analyzing goal progress:", error);
      return [];
    }
  }

  // Analyze cash flow
  private async analyzeCashFlow(overview: any, context: string): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const savingsRate = overview.savings_rate || 0;

      if (savingsRate < 10) {
        insights.push({
          id: "low-savings-rate",
          type: "cash_flow_optimization",
          priority: "high",
          title: "Low Savings Rate Detected",
          description: `Your savings rate is ${savingsRate.toFixed(1)}%. Financial experts recommend saving at least 20% of income.`,
          impact: `${(20 - savingsRate).toFixed(1)}% improvement needed`,
          confidence: 90,
          category: "Cash Flow",
          actionable: true,
          timeframe: "Next month",
          action_text: "Review budget",
          related_data: {
            current_savings_rate: savingsRate,
            target_savings_rate: 20,
          },
          created_at: new Date().toISOString(),
        });
      } else if (savingsRate > 30) {
        insights.push({
          id: "high-savings-rate",
          type: "opportunity",
          priority: "low",
          title: "Excellent Savings Rate",
          description: `Your savings rate of ${savingsRate.toFixed(1)}% is excellent! Consider investing excess savings.`,
          impact: "Investment opportunity",
          confidence: 85,
          category: "Cash Flow",
          actionable: true,
          timeframe: "Consider now",
          action_text: "Explore investments",
          related_data: {
            savings_rate: savingsRate,
          },
          created_at: new Date().toISOString(),
        });
      }

      return insights;
    } catch (error) {
      console.error("Error analyzing cash flow:", error);
      return [];
    }
  }

  // Analyze family collaboration patterns
  private async analyzeFamilyCollaboration(data: any): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      // This is a placeholder for family collaboration analysis
      // In a real implementation, this would analyze member contributions,
      // communication patterns, and collaborative goal progress

      return insights;
    } catch (error) {
      console.error("Error analyzing family collaboration:", error);
      return [];
    }
  }

  // Analyze cross-account optimization
  private async analyzeCrossAccountOptimization(data: any): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const personalBalance = data.unified.personal_vs_family.personal_balance;
      const familyBalance = data.unified.personal_vs_family.family_balance;

      if (personalBalance > familyBalance * 2) {
        insights.push({
          id: "balance-optimization",
          type: "budget_optimization",
          priority: "medium",
          title: "Balance Optimization Opportunity",
          description: "Consider transferring some personal funds to family accounts for better diversification.",
          impact: "Improved financial balance",
          confidence: 70,
          category: "Cross-Account",
          actionable: true,
          timeframe: "This month",
          action_text: "Review allocation",
          related_data: {
            personal_balance: personalBalance,
            family_balance: familyBalance,
          },
          created_at: new Date().toISOString(),
        });
      }

      return insights;
    } catch (error) {
      console.error("Error analyzing cross-account optimization:", error);
      return [];
    }
  }

  // Analyze goal alignment
  private async analyzeGoalAlignment(data: any): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      // This is a placeholder for goal alignment analysis
      // In a real implementation, this would analyze how personal and family goals
      // align and suggest optimizations

      return insights;
    } catch (error) {
      console.error("Error analyzing goal alignment:", error);
      return [];
    }
  }

  // Analyze overall financial health
  private async analyzeOverallFinancialHealth(data: any): Promise<AIInsight[]> {
    const insights: AIInsight[] = [];

    try {
      const savingsRate = data.unified.savings_rate;
      const netCashFlow = data.unified.net_cash_flow;

      if (netCashFlow < 0) {
        insights.push({
          id: "negative-cash-flow",
          type: "risk_warning",
          priority: "high",
          title: "Negative Cash Flow Detected",
          description: `Your combined expenses exceed income by $${Math.abs(netCashFlow).toFixed(2)} monthly.`,
          impact: `$${Math.abs(netCashFlow).toFixed(2)} monthly deficit`,
          confidence: 95,
          category: "Financial Health",
          actionable: true,
          timeframe: "Immediate",
          action_text: "Reduce expenses",
          related_data: {
            net_cash_flow: netCashFlow,
          },
          created_at: new Date().toISOString(),
        });
      }

      return insights;
    } catch (error) {
      console.error("Error analyzing overall financial health:", error);
      return [];
    }
  }
}

export const aiInsightsService = new AIInsightsService();
