"use client";

import React from 'react';
import { Switch } from '@/shared/components/ui/switch';
import { Label } from '@/shared/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Badge } from '@/shared/components/ui/badge';
import { Database, TestTube, AlertTriangle } from 'lucide-react';
import { useBetaFinanceType } from '../contexts/BetaFinanceTypeContext';
import { DataMode } from '../contexts/BetaFinanceTypeContext';

interface DataModeToggleProps {
  className?: string;
  showDescription?: boolean;
}

export function DataModeToggle({ className, showDescription = true }: DataModeToggleProps) {
  const { dataMode, setDataMode } = useBetaFinanceType();

  const handleToggle = (checked: boolean) => {
    const newMode: DataMode = checked ? 'real' : 'mock';
    setDataMode(newMode);
  };

  const getModeInfo = (mode: DataMode) => {
    switch (mode) {
      case 'real':
        return {
          label: 'Real Database',
          description: 'Connected to live Supabase database with persistent data',
          icon: <Database className="h-4 w-4" />,
          color: 'bg-green-500',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50'
        };
      case 'mock':
        return {
          label: 'Mock Data',
          description: 'Using demo data for testing and development',
          icon: <TestTube className="h-4 w-4" />,
          color: 'bg-amber-500',
          textColor: 'text-amber-700',
          bgColor: 'bg-amber-50'
        };
    }
  };

  const currentModeInfo = getModeInfo(dataMode);
  const isRealMode = dataMode === 'real';

  if (!showDescription) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="flex items-center space-x-2">
          {currentModeInfo.icon}
          <Label htmlFor="data-mode-toggle" className="text-sm font-medium">
            {currentModeInfo.label}
          </Label>
        </div>
        <Switch
          id="data-mode-toggle"
          checked={isRealMode}
          onCheckedChange={handleToggle}
        />
        <Badge 
          variant="secondary" 
          className={`${currentModeInfo.bgColor} ${currentModeInfo.textColor} border-0`}
        >
          {dataMode.toUpperCase()}
        </Badge>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-lg">Data Source</CardTitle>
            <Badge 
              variant="secondary" 
              className={`${currentModeInfo.bgColor} ${currentModeInfo.textColor} border-0`}
            >
              {dataMode.toUpperCase()}
            </Badge>
          </div>
          <Switch
            id="data-mode-toggle-card"
            checked={isRealMode}
            onCheckedChange={handleToggle}
          />
        </div>
        <CardDescription className="flex items-center space-x-2">
          {currentModeInfo.icon}
          <span>{currentModeInfo.description}</span>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Mock Data Info */}
          <div className={`p-3 rounded-lg border-2 transition-all ${
            dataMode === 'mock' 
              ? 'border-amber-200 bg-amber-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <TestTube className="h-4 w-4 text-amber-600" />
              <span className="font-medium text-sm">Mock Data</span>
            </div>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Demo data for testing</li>
              <li>• No database connection required</li>
              <li>• Safe for development</li>
              <li>• Data resets on refresh</li>
            </ul>
          </div>

          {/* Real Data Info */}
          <div className={`p-3 rounded-lg border-2 transition-all ${
            dataMode === 'real' 
              ? 'border-green-200 bg-green-50' 
              : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center space-x-2 mb-2">
              <Database className="h-4 w-4 text-green-600" />
              <span className="font-medium text-sm">Real Database</span>
            </div>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Live Supabase connection</li>
              <li>• Persistent data storage</li>
              <li>• Real user authentication</li>
              <li>• Production-ready features</li>
            </ul>
          </div>
        </div>

        {/* Warning for Real Mode */}
        {isRealMode && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-800">Real Database Mode Active</p>
                <p className="text-blue-700 mt-1">
                  You&apos;re now connected to the live database. All changes will be persistent 
                  and require proper authentication.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default DataModeToggle;
