# Beta Program Database Migration

This directory contains the database migration scripts needed to enable the beta program functionality.

## Quick Start

### Option 1: Using Supabase Dashboard (Recommended)

1. **Reactivate your Supabase project** (if inactive):
   - Go to [Supabase Dashboard](https://supabase.com/dashboard)
   - Find your `budget-tracker` project
   - Click "Resume" or "Reactivate" if the project is paused

2. **Apply the migration**:
   - Open the SQL Editor in your Supabase dashboard
   - Copy the contents of `apply-beta-schema.sql`
   - Paste and run the script
   - Verify the migration completed successfully

### Option 2: Using Supabase CLI

```bash
# Make sure you're logged in to Supabase CLI
supabase login

# Link to your project (replace with your project ID)
supabase link --project-ref tkjmzixriehtmjhllfhg

# Apply the migration
supabase db push

# Or run the SQL file directly
psql -h db.tkjmzixriehtmjhllfhg.supabase.co -U postgres -d postgres -f apply-beta-schema.sql
```

## What This Migration Does

### 1. **User Profile Enhancements**
- Adds beta program fields to `user_profiles` table:
  - `beta_user` - Boolean flag for beta participants
  - `beta_joined_at` - Timestamp when user joined beta
  - `beta_feedback_count` - Count of feedback submissions
  - `finance_type` - User's chosen finance journey (personal/family/combined)
  - `onboarding_completed` - Onboarding completion status
  - `enabled_features` - JSON object for feature flags
  - `dashboard_preference` - User's dashboard preference

### 2. **Feedback Collection System**
- Creates `beta_feedback` table for collecting user feedback
- Implements Row Level Security (RLS) policies
- Adds indexes for optimal performance
- Creates triggers for automatic timestamp updates

### 3. **Analytics and Reporting**
- Creates `beta_program_analytics` view for program insights
- Adds utility functions for data aggregation
- Implements helper functions for atomic operations

### 4. **Backward Compatibility**
- Updates existing users with default values
- Ensures no breaking changes to current functionality
- Maintains data integrity during migration

## Verification

After running the migration, verify it worked by checking:

```sql
-- Check if beta fields were added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND column_name LIKE '%beta%';

-- Check if beta_feedback table exists
SELECT table_name FROM information_schema.tables 
WHERE table_name = 'beta_feedback';

-- Check if analytics view exists
SELECT table_name FROM information_schema.views 
WHERE table_name = 'beta_program_analytics';

-- Test the analytics view
SELECT * FROM beta_program_analytics;
```

## Environment Variables

Make sure your `.env.local` file has the correct Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=https://tkjmzixriehtmjhllfhg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Testing the Integration

Once the migration is complete, you can test the beta program:

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Visit the beta program**:
   - Go to `http://localhost:3000/beta`
   - Sign up for beta access
   - Complete the onboarding flow
   - Test the data mode toggle in the dashboard header

3. **Switch between data modes**:
   - Use the toggle in the dashboard header dropdown
   - Watch the console logs to see when real vs mock data is being used
   - Submit feedback to test the real database integration

## Troubleshooting

### Project is Inactive
If your Supabase project is inactive:
- Go to the Supabase dashboard
- Find your project and click "Resume"
- Wait for the project to become active
- Then run the migration

### Migration Errors
If you encounter errors during migration:
- Check that you have the correct permissions
- Ensure the project is active and accessible
- Verify your database connection
- Check the Supabase logs for detailed error messages

### Connection Issues
If you can't connect to the database:
- Verify your environment variables
- Check your project URL and API keys
- Ensure your IP is whitelisted (if using IP restrictions)
- Try accessing the database through the Supabase dashboard first

## Next Steps

After successful migration:

1. **Enable beta access for test users**:
   ```sql
   UPDATE user_profiles 
   SET beta_user = true, beta_joined_at = NOW() 
   WHERE email = '<EMAIL>';
   ```

2. **Test all finance types**:
   - Personal Finance journey
   - Family Finance journey  
   - Combined Finance journey

3. **Monitor feedback collection**:
   ```sql
   SELECT * FROM beta_feedback ORDER BY created_at DESC;
   ```

4. **Check analytics**:
   ```sql
   SELECT * FROM beta_program_analytics;
   ```

The beta program is now ready for real user testing with full database integration!
